import { DEPARTMENT_IDS } from "../services/mockData";

/**
 * Department utilities for mapping and display
 */

// Create reverse mapping from ID to name
export const DEPARTMENT_ID_TO_NAME = Object.entries(DEPARTMENT_IDS).reduce(
  (acc, [name, id]) => {
    acc[id] = name;           // number key
    acc[String(id)] = name;   // string key
    return acc;
  },
  {}
);

/**
 * Get department name from department ID
 * @param {string|number} departmentId - Department ID
 * @returns {string} Department name
 */
export const getDepartmentNameById = (departmentId) => {
  if (!departmentId) return "Không xác định";
  
  return DEPARTMENT_ID_TO_NAME[departmentId] || 
         DEPARTMENT_ID_TO_NAME[String(departmentId)] || 
         DEPARTMENT_ID_TO_NAME[Number(departmentId)] || 
         `Khoa ${departmentId}`;
};

/**
 * Extract department info from user data
 * @param {Object} userData - User data object
 * @returns {Object} Department info { id, name }
 */
export const extractDepartmentFromUser = (userData) => {
  if (!userData) return { id: null, name: null };
  
  // Try to get department ID from multiple possible fields
  const departmentId = userData.departmentId || 
                      userData.departmentID || 
                      userData.DepartmentId || 
                      userData.DepartmentID;
  
  // Try to get department name from multiple possible fields
  const departmentName = userData.department || 
                        userData.departmentName || 
                        userData.Department || 
                        userData.DepartmentName;
  
  return {
    id: departmentId,
    name: departmentName && departmentName.trim() ? departmentName.trim() : null
  };
};

/**
 * Get department display name from user data
 * @param {Object} userData - User data object
 * @returns {string} Department display name
 */
export const getDepartmentFromUser = (userData) => {
  const { id, name } = extractDepartmentFromUser(userData);
  
  // If we have department name directly, return it
  if (name) {
    return name;
  }
  
  // If we have department ID, map it to name
  if (id) {
    return getDepartmentNameById(id);
  }
  
  return "Không xác định";
};

/**
 * Check if user is from blood department
 * @param {Object} userData - User data object
 * @returns {boolean} True if user is from blood department
 */
export const isBloodDepartmentUser = (userData) => {
  const { id } = extractDepartmentFromUser(userData);
  return id === 1 || id === "1"; // Blood department ID is 1
};

/**
 * Get all available departments
 * @returns {Array} Array of department objects { id, name }
 */
export const getAllDepartments = () => {
  return Object.entries(DEPARTMENT_IDS).map(([name, id]) => ({
    id,
    name
  }));
};

export default {
  DEPARTMENT_ID_TO_NAME,
  getDepartmentNameById,
  extractDepartmentFromUser,
  getDepartmentFromUser,
  isBloodDepartmentUser,
  getAllDepartments
};
