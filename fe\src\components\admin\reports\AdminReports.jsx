import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Spin,
  Select,
  Table,
  Progress,
  Divider,
  Typography,
} from "antd";
import {
  UserOutlined,
  FileTextOutlined,
  BarChartOutlined,
  DownloadOutlined,
  ReloadOutlined,
  TeamOutlined,
  HeartOutlined,
  MedicineBoxOutlined,
  CalendarOutlined,
  ExportOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
} from "@ant-design/icons";
// import reportService from "../../../services/reportService"; // Service đã bị xóa
import {
  exportToExcel,
  exportToPDF,
  formatNumber,
  formatDate,
} from "../../../utils/exportUtils";
import { toast } from "../../../utils/toastUtils";

const { Option } = Select;
const { Title, Text } = Typography;

const AdminReports = () => {
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState("month");

  // Load report data
  const loadReportData = async () => {
    setLoading(true);
    try {
      // TODO: Implement report service or use alternative data source
      toast.warning("⚠️ Chức năng báo cáo đang được phát triển");
      setReportData({
        totalUsers: 0,
        totalDonations: 0,
        totalRequests: 0,
        totalArticles: 0
      });
    } catch (error) {
      toast.error("❌ Lỗi khi tải dữ liệu báo cáo");
      console.error("Error loading report data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Export to Excel
  const handleExportExcel = async () => {
    if (!reportData) {
      toast.warning("⚠️ Không có dữ liệu để xuất");
      return;
    }

    setExporting(true);
    try {
      const result = exportToExcel(reportData, "bao_cao_thong_ke_he_thong");
      if (result.success) {
        toast.success(`📊 Đã xuất file Excel: ${result.fileName}`);
      } else {
        toast.error("❌ Lỗi khi xuất file Excel: " + result.error);
      }
    } catch (error) {
      toast.error("❌ Lỗi khi xuất file Excel");
      console.error("Export Excel error:", error);
    } finally {
      setExporting(false);
    }
  };

  // Export to PDF
  const handleExportPDF = async () => {
    if (!reportData) {
      toast.warning("⚠️ Không có dữ liệu để xuất");
      return;
    }

    setExporting(true);
    try {
      const result = exportToPDF(reportData, "bao_cao_thong_ke_he_thong");
      if (result.success) {
        toast.success(`📄 Đã xuất file PDF: ${result.fileName}`);
      } else {
        toast.error("❌ Lỗi khi xuất file PDF: " + result.error);
      }
    } catch (error) {
      toast.error("❌ Lỗi khi xuất file PDF");
      console.error("Export PDF error:", error);
    } finally {
      setExporting(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadReportData();
  }, [selectedPeriod]);

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>Đang tải dữ liệu báo cáo...</div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Text type="secondary">Không có dữ liệu báo cáo</Text>
        <br />
        <Button
          type="primary"
          onClick={loadReportData}
          style={{ marginTop: 16 }}
        >
          Tải lại
        </Button>
      </div>
    );
  }

  return (
    <div>
      {/* Controls */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: 24,
        }}
      >
        <div>
          <Text type="secondary">
            Cập nhật lần cuối: {formatDate(reportData.overview.generatedAt)}
          </Text>
        </div>
        <Space>
          <Select
            value={selectedPeriod}
            onChange={setSelectedPeriod}
            style={{ width: 120 }}
          >
            <Option value="week">Tuần</Option>
            <Option value="month">Tháng</Option>
            <Option value="quarter">Quý</Option>
            <Option value="year">Năm</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadReportData}
            loading={loading}
          >
            Làm mới
          </Button>
          <Button
            type="primary"
            icon={<FileExcelOutlined />}
            onClick={handleExportExcel}
            loading={exporting}
          >
            Xuất Excel
          </Button>
          <Button
            type="primary"
            icon={<FilePdfOutlined />}
            onClick={handleExportPDF}
            loading={exporting}
            style={{ backgroundColor: "#ff4d4f", borderColor: "#ff4d4f" }}
          >
            Xuất PDF
          </Button>
        </Space>
      </div>

      {/* Overview Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Tổng người dùng"
              value={reportData.overview.totalUsers}
              prefix={<UserOutlined style={{ color: "#1890ff" }} />}
              formatter={(value) => formatNumber(value)}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Yêu cầu máu"
              value={reportData.overview.totalBloodRequests}
              prefix={<HeartOutlined style={{ color: "#ff4d4f" }} />}
              formatter={(value) => formatNumber(value)}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Bài viết"
              value={reportData.overview.totalArticles}
              prefix={<FileTextOutlined style={{ color: "#52c41a" }} />}
              formatter={(value) => formatNumber(value)}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Tin tức"
              value={reportData.overview.totalNews}
              prefix={<BarChartOutlined style={{ color: "#722ed1" }} />}
              formatter={(value) => formatNumber(value)}
            />
          </Card>
        </Col>
      </Row>

      {/* User Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} md={12}>
          <Card title="Thống kê người dùng" extra={<TeamOutlined />}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="Đang hoạt động"
                  value={reportData.users.active}
                  valueStyle={{ color: "#3f8600" }}
                  formatter={(value) => formatNumber(value)}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Không hoạt động"
                  value={reportData.users.inactive}
                  valueStyle={{ color: "#cf1322" }}
                  formatter={(value) => formatNumber(value)}
                />
              </Col>
            </Row>
            <Divider />
            <div>
              <Text strong>Theo vai trò:</Text>
              <div style={{ marginTop: 8 }}>
                <div>
                  Quản trị viên:{" "}
                  <Text strong>
                    {formatNumber(reportData.users.byRole.admin)}
                  </Text>
                </div>
                <div>
                  Quản lý:{" "}
                  <Text strong>
                    {formatNumber(reportData.users.byRole.manager)}
                  </Text>
                </div>
                <div>
                  Bác sĩ:{" "}
                  <Text strong>
                    {formatNumber(reportData.users.byRole.doctor)}
                  </Text>
                </div>
                <div>
                  Thành viên:{" "}
                  <Text strong>
                    {formatNumber(reportData.users.byRole.member)}
                  </Text>
                </div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="Thống kê yêu cầu máu" extra={<MedicineBoxOutlined />}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="Chờ duyệt"
                  value={reportData.bloodRequests.byStatus.pending}
                  valueStyle={{ color: "#fa8c16" }}
                  formatter={(value) => formatNumber(value)}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Hoàn thành"
                  value={reportData.bloodRequests.byStatus.completed}
                  valueStyle={{ color: "#3f8600" }}
                  formatter={(value) => formatNumber(value)}
                />
              </Col>
            </Row>
            <Divider />
            <div>
              <Text strong>Tổng lượng máu yêu cầu:</Text>
              <div
                style={{
                  fontSize: "24px",
                  fontWeight: "bold",
                  color: "#ff4d4f",
                }}
              >
                {formatNumber(reportData.bloodRequests.totalQuantity)} ml
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Blood Type Statistics */}
      {reportData.users.byBloodType &&
        Object.keys(reportData.users.byBloodType).length > 0 && (
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24}>
              <Card title="Thống kê theo nhóm máu" extra={<HeartOutlined />}>
                <Row gutter={[16, 16]}>
                  {Object.entries(reportData.users.byBloodType).map(
                    ([bloodType, count]) => (
                      <Col xs={12} sm={8} md={6} key={bloodType}>
                        <Card size="small" style={{ textAlign: "center" }}>
                          <Statistic
                            title={`Nhóm ${bloodType}`}
                            value={count}
                            valueStyle={{ color: "#ff4d4f" }}
                            formatter={(value) => formatNumber(value)}
                          />
                        </Card>
                      </Col>
                    )
                  )}
                </Row>
              </Card>
            </Col>
          </Row>
        )}

      {/* Department Statistics */}
      {reportData.users.byDepartment &&
        Object.keys(reportData.users.byDepartment).length > 0 && (
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24}>
              <Card
                title="Thống kê bác sĩ theo khoa"
                extra={<MedicineBoxOutlined />}
              >
                <Row gutter={[16, 16]}>
                  {Object.entries(reportData.users.byDepartment).map(
                    ([department, count]) => (
                      <Col xs={24} sm={12} md={8} key={department}>
                        <Card size="small">
                          <Statistic
                            title={department}
                            value={count}
                            suffix="bác sĩ"
                            valueStyle={{ color: "#1890ff" }}
                            formatter={(value) => formatNumber(value)}
                          />
                        </Card>
                      </Col>
                    )
                  )}
                </Row>
              </Card>
            </Col>
          </Row>
        )}

      {/* Monthly Requests Chart Data */}
      {reportData.bloodRequests.byMonth &&
        Object.keys(reportData.bloodRequests.byMonth).length > 0 && (
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24}>
              <Card title="Yêu cầu máu theo tháng" extra={<CalendarOutlined />}>
                <Table
                  dataSource={Object.entries(reportData.bloodRequests.byMonth)
                    .sort(([a], [b]) => new Date(a) - new Date(b))
                    .map(([month, count], index) => ({
                      key: index,
                      month,
                      count,
                      percentage: (
                        (count / reportData.bloodRequests.total) *
                        100
                      ).toFixed(1),
                    }))}
                  columns={[
                    {
                      title: "Tháng",
                      dataIndex: "month",
                      key: "month",
                    },
                    {
                      title: "Số lượng yêu cầu",
                      dataIndex: "count",
                      key: "count",
                      render: (value) => formatNumber(value),
                    },
                    {
                      title: "Tỷ lệ",
                      dataIndex: "percentage",
                      key: "percentage",
                      render: (value) => (
                        <div>
                          <Progress percent={parseFloat(value)} size="small" />
                          <Text>{value}%</Text>
                        </div>
                      ),
                    },
                  ]}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        )}

      {/* Export Actions */}
      <Card style={{ textAlign: "center", backgroundColor: "#fafafa" }}>
        <Title level={4}>Xuất báo cáo</Title>
        <Text type="secondary" style={{ display: "block", marginBottom: 16 }}>
          Tải xuống báo cáo chi tiết dưới định dạng Excel hoặc PDF
        </Text>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            icon={<FileExcelOutlined />}
            onClick={handleExportExcel}
            loading={exporting}
            style={{ minWidth: 150 }}
          >
            Tải Excel
          </Button>
          <Button
            type="primary"
            size="large"
            icon={<FilePdfOutlined />}
            onClick={handleExportPDF}
            loading={exporting}
            style={{
              backgroundColor: "#ff4d4f",
              borderColor: "#ff4d4f",
              minWidth: 150,
            }}
          >
            Tải PDF
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default AdminReports;
