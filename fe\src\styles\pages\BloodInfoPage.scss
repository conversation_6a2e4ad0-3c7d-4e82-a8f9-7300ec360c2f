@use "../base/variables" as vars;
@use "../base/mixin" as mix;

// Import fonts
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");

// Variables
$primary-color: #1a365d;
$secondary-color: #2c5282;
$accent-color: #4299e1;
$text-color: #2d3748;
$background-color: #f7fafc;
$header-gradient: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
$transition-duration: 0.3s;

.guest-home-page {
  @include mix.reset();
  @include mix.body-base();
  width: 100%;
  overflow-x: hidden;
  background: $background-color;
  min-height: 100vh;
  font-family: "Roboto", sans-serif;

  .content-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: vars.$spacing-xl vars.$spacing-base;

    // Page Header
    .page-header {
      position: relative;
      height: 240px;
      background: linear-gradient(
          135deg,
          rgba(66, 153, 225, 0.1) 0%,
          transparent 50%
        ),
        /* Lighter, more transparent large streak */
          linear-gradient(225deg, rgba(44, 96, 192, 0.15) 0%, transparent 50%),
        /* Medium, less transparent streak */
          linear-gradient(45deg, rgba(26, 54, 93, 0.2) 0%, transparent 50%),
        /* Darker, more opaque streak */
          linear-gradient(135deg, #0f1e3d 0%, #2c64b4 100%); /* Base gradient */
      background-size: 300% 300%, 250% 250%, 200% 200%, cover;
      background-position: -50% -50%, 0% 0%, 50% 50%, center center;
      background-repeat: no-repeat;
      margin-bottom: 48px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: white;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);

      &::before {
        content: none; /* Remove the old pattern */
      }

      .header-content {
        position: relative;
        z-index: 1;
        max-width: 800px;
        padding: 0 24px;
        text-align: center;
        justify-content: center;

        h1 {
          font-family: "Roboto", sans-serif;
          font-size: 2.8rem;
          font-weight: 700;
          margin-bottom: 16px;
          letter-spacing: 0.5px;
          text-transform: uppercase;
          background: linear-gradient(to right, #ffffff, #e2e8f0);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        p {
          font-family: "Roboto", sans-serif;
          font-size: 1.2rem;
          font-weight: 400;
          line-height: 1.6;
          color: rgba(255, 255, 255, 0.9);
          max-width: 600px;
          margin: 0 auto;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }
    }

    // Controls Section
    .controls-section {
      background: #fff;
      padding: 24px;
      border-radius: 16px;
      margin-bottom: 32px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;
      justify-content: space-between;

      .search-controls {
        flex: 1;
        min-width: 280px;

        .search-input {
          border-radius: 8px;
          height: 40px;

          .ant-input {
            font-size: 1rem;
          }

          .search-icon {
            color: #1890ff;
          }
        }
      }

      .filter-controls {
        .ant-select {
          min-width: 200px;

          .ant-select-selector {
            border-radius: 8px;
            height: 40px;
            display: flex;
            align-items: center;
          }
        }

        .filter-icon {
          color: #1890ff;
          margin-right: 8px;
        }
      }
    }

    // Category Section
    .category-section {
      margin-bottom: vars.$spacing-xl;
      padding: vars.$spacing-lg;
      border-radius: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

      // Xen kẽ màu nền
      &:nth-child(odd) {
        background: #ffffff; // Màu trắng cho section lẻ
      }

      &:nth-child(even) {
        background: #f8fafc; // Màu xám nhạt cho section chẵn
      }

      .category-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: vars.$spacing-lg;
        padding-bottom: vars.$spacing-md;
        border-bottom: 3px solid #e2e8f0;

        .category-title {
          @include mix.heading(2rem, #1e293b);
          font-weight: 700;
          letter-spacing: 1px;
          margin: 0;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            bottom: -12px;
            left: 0;
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #2b6cb0, #4299e1);
            border-radius: 2px;
          }
        }

        .category-count {
          background: linear-gradient(135deg, #2b6cb0, #4299e1);
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: 600;
          box-shadow: 0 2px 10px rgba(43, 108, 176, 0.2);
        }
      }

      // Document Grid
      .document-grid {
        .ant-col {
          margin-bottom: vars.$spacing-md;
        }

        .document-card {
          border: none;
          border-radius: 20px;
          overflow: hidden;
          height: 100%;
          background: white;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          cursor: pointer;

          &:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);

            .card-cover img {
              transform: scale(1.05);
            }

            .card-overlay {
              opacity: 1;
            }
          }

          .card-cover {
            position: relative;
            height: 150px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.5s ease;
            }

            .card-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(
                135deg,
                rgba(43, 108, 176, 0.8),
                rgba(66, 153, 225, 0.8)
              );
              display: flex;
              align-items: center;
              justify-content: center;
              opacity: 0;
              transition: all 0.3s ease;

              .views-badge {
                background: rgba(255, 255, 255, 0.9);
                color: #1e293b;
                padding: 6px 12px;
                border-radius: 20px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 0.8rem;
                backdrop-filter: blur(10px);
              }
            }
          }

          .ant-card-body {
            padding: 0;
          }

          .card-content {
            padding: vars.$spacing-md;
            display: flex;
            flex-direction: column;
            height: 220px;

            .card-meta {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              margin-bottom: vars.$spacing-sm;

              .category-tag {
                display: flex;
                align-items: center;
                gap: 6px;
                color: #2b6cb0;
                font-weight: 600;
                font-size: 0.8rem;
              }
            }

            .document-title {
              @include mix.heading(1.1rem, #1e293b);
              margin-bottom: vars.$spacing-sm;
              font-weight: 700;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              overflow: hidden;
            }

            .document-summary {
              @include mix.text(0.9rem, #64748b);
              line-height: 1.6;
              flex-grow: 1;
              margin-bottom: vars.$spacing-sm;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              overflow: hidden;
              max-height: 4.8em; // 3 dòng
            }

            .read-more-btn {
              background: linear-gradient(135deg, #2b6cb0 0%, #4299e1 100%);
              border: none;
              border-radius: 12px;
              padding: 8px 16px;
              font-weight: 600;
              font-size: 0.9rem;
              height: auto;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(43, 108, 176, 0.3);
                background: linear-gradient(135deg, #22548f 0%, #3182ce 100%);
              }
            }

            .highlight-text {
              background: none;
              // color: #ff4d4f !important;
              font-weight: bold;
              padding: 0 2px;
              border-radius: 2px;
              transition: background 0.2s;
            }
          }
        }
      }

      // Pagination
      .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: vars.$spacing-lg;

        .ant-pagination {
          .ant-pagination-item {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            transition: all 0.3s ease;
            margin: 0 4px;

            a {
              color: #1e293b;
              font-weight: 500;
              font-family: "Roboto", sans-serif;
            }

            &:hover {
              border-color: #2b6cb0;
              background: #f8fafc;
            }
          }

          .ant-pagination-item-active {
            border-radius: 8px;
            background: linear-gradient(135deg, #2b6cb0, #4299e1);
            border-color: #4299e1;

            a {
              color: white;
            }

            &:hover {
              border-color: #4299e1;
            }
          }

          .ant-pagination-prev,
          .ant-pagination-next {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            transition: all 0.3s ease;

            .ant-pagination-item-link {
              color: #1e293b;
              border-radius: 8px;
            }

            &:hover {
              border-color: #2b6cb0;
              background: #f8fafc;
            }
          }

          .ant-pagination-disabled {
            .ant-pagination-item-link {
              color: #94a3b8;
            }
          }
        }
      }
    }

    // No Results
    .no-results {
      text-align: center;
      padding: vars.$spacing-xl * 2;
      background: white;
      border-radius: 20px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

      .no-results-icon {
        font-size: 4rem;
        margin-bottom: vars.$spacing-lg;
        opacity: 0.6;
      }

      h3 {
        @include mix.heading(1.5rem, #1e293b);
        margin-bottom: vars.$spacing-sm;
        font-weight: 600;
      }

      p {
        @include mix.text(1rem, #64748b);
        margin: 0;
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .content-section {
      padding: vars.$spacing-lg vars.$spacing-sm;

      .page-header {
        padding: 40px 20px;
        margin-bottom: 32px;

        .header-content {
          h1 {
            font-size: 2rem;
          }

          p {
            font-size: 1.1rem;
          }
        }
      }

      .category-header {
        flex-direction: column;
        align-items: flex-start;
        gap: vars.$spacing-sm;
      }

      .document-card .card-content {
        height: auto;
        min-height: 220px;
      }
    }
  }

  // Animation keyframes
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .document-card {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  // Stagger animation for cards
  @for $i from 1 through 12 {
    .document-card:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

// =============================
// Article Detail Page
// =============================

.article-detail-page {
  background: $background-color;
  min-height: 100vh;
  padding: 32px 0;

  .article-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px;

    .back-button {
      margin-bottom: 24px;
      color: #1890ff;
      font-weight: 500;
      padding: 0;
      height: auto;

      &:hover {
        color: #096dd9;
      }
    }

    .article-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

      .article-header {
        text-align: center;
        padding: 32px 24px;
        background: $header-gradient;
        color: white;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
          opacity: 0.5;
        }

        .article-title {
          position: relative;
          z-index: 1;
          font-size: 2.2rem;
          font-weight: 700;
          margin-bottom: 16px;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .article-tags {
          position: relative;
          z-index: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin-top: 16px;

          .ant-tag {
            background: #f3f8fd !important; // pastel blue
            color: #3578e5;
            border: 1px solid #e3eaf3;
            font-weight: 500;
            font-size: 0.95rem;
            padding: 3px 14px;
            border-radius: 16px;
            box-shadow: none;
            transition: background 0.2s, color 0.2s, border 0.2s;
            letter-spacing: 0.01em;
            display: inline-flex;
            align-items: center;
            margin-bottom: 2px;
            margin-right: 2px;
            font-family: inherit;
            cursor: default;
            &:hover {
              background: #e6f0fa !important;
              color: #2456a6;
              border-color: #d0d8e8;
            }
          }
        }
      }

      .article-meta {
        margin-bottom: 24px;
        color: #64748b;
        font-size: 0.95rem;

        .anticon {
          color: #1890ff;
        }
      }

      .article-image-wrapper {
        margin: 0 -24px 24px;
        height: 400px;
        overflow: hidden;
        position: relative;

        .article-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;
        }

        &:hover .article-image {
          transform: scale(1.02);
        }
      }

      .article-content {
        padding: 32px;
        color: $text-color;
        line-height: 1.8;
        font-size: 1.1rem;

        p {
          margin-bottom: 1.5em;
        }

        img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 1.5em 0;
        }

        h2,
        h3,
        h4 {
          color: $primary-color;
          margin: 1.5em 0 0.75em;
        }

        h2 {
          font-size: 1.8rem;
          font-weight: 700;
        }

        h3 {
          font-size: 1.5rem;
          font-weight: 600;
        }

        h4 {
          font-size: 1.3rem;
          font-weight: 500;
        }

        ul,
        ol {
          margin: 1.5em 0;
          padding-left: 2em;
        }

        li {
          margin-bottom: 0.5em;
        }

        blockquote {
          border-left: 4px solid $accent-color;
          padding-left: 1em;
          margin: 1.5em 0;
          color: #666;
          font-style: italic;
        }

        code {
          background: #f1f1f1;
          padding: 0.2em 0.4em;
          border-radius: 4px;
          font-family: "Courier New", monospace;
        }

        pre {
          background: #f1f1f1;
          padding: 1em;
          border-radius: 8px;
          overflow-x: auto;
          margin: 1.5em 0;

          code {
            background: none;
            padding: 0;
          }
        }
      }
    }
  }
}

// Responsive Design for Article Detail
@media (max-width: 768px) {
  .article-detail-page {
    padding: 16px 0;

    .article-container {
      padding: 0 16px;

      .article-card {
        .article-header {
          .article-title {
            font-size: 1.8rem;
          }
        }

        .article-image-wrapper {
          height: 240px;
          margin: 0 -16px 16px;
        }

        .article-content {
          padding: 24px 16px;
          font-size: 1rem;

          .article-meta {
            margin-bottom: 10px;

            .article-date {
              font-size: 0.85rem;

              .date-icon {
                font-size: 0.75rem;
              }
            }
          }

          h2 {
            font-size: 1.6rem;
          }

          h3 {
            font-size: 1.4rem;
          }

          h4 {
            font-size: 1.2rem;
          }
        }
      }
    }
  }
}

// Button style
.ant-btn-primary,
.read-more-btn {
  background: #1890ff;
  border: none;
  color: #fff;
  font-weight: 600;
  border-radius: 8px;
  padding: 8px 20px;
  font-size: 1rem;
  transition: background 0.3s, box-shadow 0.3s;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);

  &:hover,
  &:focus {
    background: #1765ad;
    color: #fff;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.18);
  }
}

// Responsive
@media (max-width: 900px) {
  .article-detail-page .ant-card {
    max-width: 98vw;
    padding: 0 0 12px 0;
  }
  .article-detail-page .ant-card-cover img {
    max-height: 180px;
  }
  .article-detail_page .ant-card-body {
    padding: 18px 8px 8px 8px;
  }
}

// Article Groups and Cards
.article-group {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 32px;

  .group-header {
    padding: 24px;
    background: $header-gradient;
    color: white;
    position: relative;
    overflow: hidden;
    text-align: center;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
      opacity: 0.5;
    }

    .group-header-content {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .group-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        color: white;
      }

      .group-count {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
        backdrop-clip: text;
        -webkit-background-clip: text;
      }
    }
  }

  .article-grid {
    padding: 24px;

    .article-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      margin-bottom: 24px;

      @media (max-width: 1200px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }
  }

  .article-card {
    height: 100%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    transition: all $transition-duration ease;
    border: 1px solid #e2e8f0;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .article-image-container {
      position: relative;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform $transition-duration ease;
      }

      &:hover img {
        transform: scale(1.05);
      }
    }

    .article-content {
      padding: 20px;

      .article-meta {
        margin-bottom: 12px;

        .article-date {
          display: flex;
          align-items: center;
          gap: 6px;
          color: #666;
          font-size: 0.9rem;

          .date-icon {
            font-size: 0.8rem;
          }
        }
      }

      .article-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: $text-color;
        margin-bottom: 12px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        max-height: 2.8em; // 2 dòng
      }

      .article-description {
        color: #4a5568;
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 16px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        max-height: 4.8em; // 3 dòng
      }

      .article-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .ant-tag {
          background: #e6f7ff; // màu xanh nhạt
          color: #1890ff;
          border: none;
          font-weight: 500;
          font-size: 0.95rem;
          padding: 4px 12px;
          border-radius: 6px;
          backdrop-clip: text;
          -webkit-background-clip: text;
          transition: all $transition-duration ease;

          &:hover {
            background: #bee3f8;
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e2e8f0;

    .article-pagination {
      .ant-pagination-item {
        border-radius: 6px;
        margin: 0 4px;

        &-active {
          background: $accent-color;
          border-color: $accent-color;

          a {
            color: white;
          }
        }
      }

      .ant-pagination-prev,
      .ant-pagination-next {
        .ant-pagination-item-link {
          border-radius: 6px;
        }
      }

      .ant-pagination-total-text {
        margin-right: 16px;
        color: #64748b;
      }
    }
  }
}

// Filter Section
.controls-section {
  background: #fff;
  padding: 24px;
  border-radius: 16px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  justify-content: space-between;

  .search-controls {
    flex: 1;
    min-width: 280px;

    .search-input {
      border-radius: 8px;
      height: 40px;

      .ant-input {
        font-size: 1rem;
      }

      .search-icon {
        color: #1890ff;
      }
    }
  }

  .filter-controls {
    .ant-select {
      min-width: 200px;

      .ant-select-selector {
        border-radius: 8px;
        height: 40px;
        display: flex;
        align-items: center;
      }
    }

    .filter-icon {
      color: #1890ff;
      margin-right: 8px;
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .article-group {
    .article-grid {
      .article-list {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    height: auto;
    min-height: 200px;
    padding: 40px 20px;

    .header-content {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1.1rem;
      }
    }
  }

  .article-group {
    .article-grid {
      .article-list {
        grid-template-columns: 1fr;
      }
    }

    .group-header {
      padding: 16px;

      .group-header-content {
        .group-title {
          font-size: 1.3rem;
        }
      }
    }
  }

  .article-detail-page {
    .article-container {
      padding: 0 16px;

      .article-card {
        .article_header {
          padding: 24px 16px;

          .article-title {
            font-size: 1.8rem;
          }
        }

        .article-content {
          padding: 24px 16px;
          font-size: 1rem;

          h2 {
            font-size: 1.6rem;
          }

          h3 {
            font-size: 1.4rem;
          }

          h4 {
            font-size: 1.2rem;
          }
        }
      }
    }
  }
}

// Error and Loading States
.error-container,
.loading-container {
  .title-icon {
    color: #1890ff;
    margin-right: 8px;
  }
}

// Fade-in animation for article card
.article-card.fade-in {
  animation: fadeIn 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: none;
  }
}

.pagination-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin: 32px 0 0 0;
  gap: 24px;
  width: 100%;

  .pagination-total {
    flex: 1;
    color: #888;
    font-size: 1rem;
    font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    text-align: left;
  }

  .ant-pagination {
    flex-shrink: 0;
    .ant-pagination-item {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      background: white;
      transition: all 0.3s ease;
      margin: 0 4px;

      a {
        color: #1e293b;
        font-weight: 500;
        font-family: "Roboto", sans-serif;
      }

      &:hover {
        border-color: #2b6cb0;
        background: #f8fafc;
      }
    }

    .ant-pagination-item-active {
      border-radius: 8px;
      background: linear-gradient(135deg, #2b6cb0, #4299e1);
      border-color: #4299e1;

      a {
        color: white;
      }

      &:hover {
        border-color: #4299e1;
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      background: white;
      transition: all 0.3s ease;

      .ant-pagination-item-link {
        color: #1e293b;
        border-radius: 8px;
      }

      &:hover {
        border-color: #2b6cb0;
        background: #f8fafc;
      }
    }

    .ant-pagination-disabled {
      .ant-pagination-item-link {
        color: #94a3b8;
      }
    }
  }
}

.guest-home-page .ant-pagination-item a {
  color: #1e293b;
  font-weight: 500;
  font-family: "Roboto", sans-serif;
}

.guest-home-page .ant-pagination-item-active a {
  color: white;
}
