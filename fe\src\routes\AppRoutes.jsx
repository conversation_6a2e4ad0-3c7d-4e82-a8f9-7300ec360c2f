import { createBrowserRouter } from "react-router-dom";
import NotFoundPage from "../pages/error/NotFoundPage";
import ForbiddenPage from "../pages/error/ForbiddenPage";
import GuestHomePage from "../pages/guest/GuestHomePage";
import LoginPage from "../pages/auth/LoginPage";
import RegisterPage from "../pages/auth/RegisterPage";
import EmailVerificationPage from "../pages/auth/EmailVerificationPage";
import ForgotPasswordPage from "../pages/auth/ForgotPasswordPage";
import ResetPasswordPage from "../pages/auth/ResetPasswordPage";
import GoogleCallback from "../components/auth/GoogleCallback";

import BloodInfoPage from "../pages/guest/BloodInfoPage";
import BlogPage from "../pages/guest/BlogPage";
import BlogDetailPage from "../pages/guest/BlogDetailPage";
import ArticleDetailPage from "../pages/guest/ArticleDetailPage";
import BloodDonationGuide from "../pages/guest/DonationGuide";
import ManagerHomePage from "../pages/manager/ManagerHomePage";
import ManagerDashboard from "../pages/manager/ManagerDashboard";
import BloodRequestsPage from "../pages/manager/BloodRequestsPage";

import BloodInventoryManagement from "../pages/manager/BloodInventoryManagement";

import MemberHomePage from "../pages/member/MemberHomePage";
import MemberBlogPage from "../pages/member/MemberBlogPage";

import MemberBloodInfoPage from "../pages/member/MemberBloodInfoPage";

import MemberDonationGuide from "../pages/member/MemberDonationGuide";
import MemberInfoPage from "../pages/member/MemberInfoPage";
import ChangePasswordPage from "../pages/member/ChangePasswordPage";
import MemberNavbar from "../components/member/MemberNavbar";

import DoctorDashboard from "../pages/doctor/DoctorDashboard";
import DoctorBloodRequestsPage from "../pages/doctor/DoctorBloodRequestsPage";
import DoctorDonorManagementPage from "../pages/doctor/DoctorDonorManagementPage";

import BloodInventoryViewPage from "../pages/doctor/BloodInventoryViewPage";
import DonationSchedulePage from "../pages/manager/DonationSchedulePage";
import EligibleDonorsPage from "../pages/manager/EligibleDonorsPage";

import BloodDonationFormPage from "../pages/member/BloodDonationFormPage";
import BloodRequestFormPage from "../pages/member/BloodRequestFormPage";
import ActivityHistoryPage from "../pages/member/ActivityHistoryPage";
import NotificationsPage from "../pages/member/NotificationsPage";

// Admin imports
import AdminDashboard from "../pages/admin/AdminDashboard";
import UserManagement from "../pages/admin/UserManagement";
import BlogApproval from "../pages/admin/BlogApproval";
import Reports from "../pages/admin/Reports";
import SystemSettings from "../pages/admin/SystemSettings";

// Doctor Blog Management
import DoctorBlogManagement from "../pages/doctor/BlogManagement";
import DoctorBlogEditor from "../pages/doctor/BlogEditor";
import CreateArticlePage from "../pages/doctor/CreateArticlePage";
import CreateNewsPage from "../pages/doctor/CreateNewsPage";

// Profile Pages
import DoctorProfilePage from "../pages/doctor/DoctorProfilePage";
import ManagerProfilePage from "../pages/manager/ManagerProfilePage";
import AdminProfilePage from "../pages/admin/AdminProfilePage";

// Change Password Pages
import DoctorChangePasswordPage from "../pages/doctor/DoctorChangePasswordPage";
import ManagerChangePasswordPage from "../pages/manager/ManagerChangePasswordPage";

import ProtectedRoute, {
  MemberRoute,
  DoctorRoute,
  ManagerRoute,
  AdminRoute,
} from "../components/common/ProtectedRoute";

const router = createBrowserRouter([
  {
    path: "/",
    element: <GuestHomePage />,
  },
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/register",
    element: <RegisterPage />,
  },
  {
    path: "/forgot-password",
    element: <ForgotPasswordPage />,
  },
  {
    path: "/reset-password",
    element: <ResetPasswordPage />,
  },
  {
    path: "/register/verify-email",
    element: <EmailVerificationPage />,
  },
  {
    path: "/signin-google",
    element: <GoogleCallback />,
  },
  {
    path: "/403",
    element: <ForbiddenPage />,
  },
  {
    path: "/404",
    element: <NotFoundPage />,
  },
  {
    path: "/blood-info",
    element: <BloodInfoPage />,
  },
  {
    path: "/blog",
    element: <BlogPage />,
  },
  {
    path: "/donation-guide",
    element: <BloodDonationGuide />,
  },
  {
    path: "/manager",
    element: (
      <ManagerRoute>
        <ManagerDashboard />
      </ManagerRoute>
    ),
  },
  {
    path: "/manager/home",
    element: (
      <ManagerRoute>
        <ManagerHomePage />
      </ManagerRoute>
    ),
  },
  {
    path: "/manager/blood-requests",
    element: (
      <ManagerRoute>
        <BloodRequestsPage />
      </ManagerRoute>
    ),
  },

  {
    path: "/manager/blood-inventory",
    element: (
      <ManagerRoute>
        <BloodInventoryManagement />
      </ManagerRoute>
    ),
  },
  {
    path: "/manager/donation-schedule",
    element: (
      <ManagerRoute>
        <DonationSchedulePage />
      </ManagerRoute>
    ),
  },
  {
    path: "/manager/eligible-donors",
    element: (
      <ManagerRoute>
        <EligibleDonorsPage />
      </ManagerRoute>
    ),
  },
  {
    path: "/manager/profile",
    element: (
      <ManagerRoute>
        <ManagerProfilePage />
      </ManagerRoute>
    ),
  },
  {
    path: "/manager/change-password",
    element: (
      <ManagerRoute>
        <ManagerChangePasswordPage />
      </ManagerRoute>
    ),
  },
  {
    path: "/doctor",
    element: (
      <DoctorRoute>
        <DoctorDashboard />
      </DoctorRoute>
    ),
  },
  {
    path: "/doctor/blood-requests",
    element: (
      <DoctorRoute>
        <DoctorBloodRequestsPage />
      </DoctorRoute>
    ),
  },

  {
    path: "/doctor/donor-management",
    element: (
      <DoctorRoute>
        <DoctorDonorManagementPage />
      </DoctorRoute>
    ),
  },

  {
    path: "/doctor/blood-inventory",
    element: (
      <DoctorRoute>
        <BloodInventoryViewPage />
      </DoctorRoute>
    ),
  },
  {
    path: "/doctor/profile",
    element: (
      <DoctorRoute>
        <DoctorProfilePage />
      </DoctorRoute>
    ),
  },
  {
    path: "/doctor/change-password",
    element: (
      <DoctorRoute>
        <DoctorChangePasswordPage />
      </DoctorRoute>
    ),
  },
  {
    path: "/member",
    element: (
      <MemberRoute>
        <MemberHomePage />
      </MemberRoute>
    ),
  },

  {
    path: "/member/blood-info",
    element: (
      <MemberRoute>
        <MemberBloodInfoPage />
      </MemberRoute>
    ),
  },
  {
    path: "/member/blog",
    element: (
      <MemberRoute>
        <MemberBlogPage />
      </MemberRoute>
    ),
  },
  {
    path: "/member/donation-guide",
    element: (
      <MemberRoute>
        <MemberDonationGuide />
      </MemberRoute>
    ),
  },
  {
    path: "/member/profile",
    element: (
      <MemberRoute>
        <MemberInfoPage />
      </MemberRoute>
    ),
  },
  {
    path: "/member/change-password",
    element: (
      <MemberRoute>
        <ChangePasswordPage />
      </MemberRoute>
    ),
  },
  {
    path: "/member/blood-donation-form",
    element: (
      <MemberRoute>
        <BloodDonationFormPage />
      </MemberRoute>
    ),
  },

  {
    path: "/member/blood-request-form",
    element: (
      <MemberRoute>
        <BloodRequestFormPage />
      </MemberRoute>
    ),
  },
  {
    path: "/member/activity-history",
    element: (
      <MemberRoute>
        <ActivityHistoryPage />
      </MemberRoute>
    ),
  },

  {
    path: "/member/notifications",
    element: (
      <MemberRoute>
        <NotificationsPage />
      </MemberRoute>
    ),
  },
  {
    path: "/member/blood-info/:id",
    element: (
      <MemberRoute>
        <ArticleDetailPage />
      </MemberRoute>
    ),
  },
  {
    path: "/member/blog/:postId",
    element: (
      <MemberRoute>
        <BlogDetailPage />
      </MemberRoute>
    ),
  },
  {
    path: "/articles/:id",
    element: <ArticleDetailPage />,
  },
  {
    path: "/blood-info/:id",
    element: <ArticleDetailPage />,
  },
  {
    path: "/blog/:postId",
    element: <BlogDetailPage />,
  },

  // === ADMIN ROUTES ===
  {
    path: "/admin/dashboard",
    element: (
      <AdminRoute>
        <AdminDashboard />
      </AdminRoute>
    ),
  },
  {
    path: "/admin/users",
    element: (
      <AdminRoute>
        <UserManagement />
      </AdminRoute>
    ),
  },
  {
    path: "/admin/users/:userType",
    element: (
      <AdminRoute>
        <UserManagement />
      </AdminRoute>
    ),
  },

  {
    path: "/admin/blogs",
    element: (
      <AdminRoute>
        <BlogApproval />
      </AdminRoute>
    ),
  },
  {
    path: "/admin/reports",
    element: (
      <AdminRoute>
        <Reports />
      </AdminRoute>
    ),
  },
  {
    path: "/admin/system",
    element: (
      <AdminRoute>
        <SystemSettings />
      </AdminRoute>
    ),
  },
  {
    path: "/admin/profile",
    element: (
      <AdminRoute>
        <AdminProfilePage />
      </AdminRoute>
    ),
  },

  // === DOCTOR BLOG ROUTES ===
  {
    path: "/doctor/blog",
    element: (
      <DoctorRoute>
        <DoctorBlogManagement />
      </DoctorRoute>
    ),
  },
  {
    path: "/doctor/blog/create-article",
    element: (
      <DoctorRoute>
        <CreateArticlePage />
      </DoctorRoute>
    ),
  },
  {
    path: "/doctor/blog/create-news",
    element: (
      <DoctorRoute>
        <CreateNewsPage />
      </DoctorRoute>
    ),
  },
  {
    path: "/doctor/blog/edit/:id",
    element: (
      <DoctorRoute>
        <DoctorBlogEditor />
      </DoctorRoute>
    ),
  },

  {
    path: "*",
    element: <NotFoundPage />,
  },
]);

export default router;
