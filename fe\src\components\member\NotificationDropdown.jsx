import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import NotificationService from "../../services/notificationService";
import { useNotification } from "../../contexts/NotificationContext";
import { useAuth } from "../../contexts/AuthContext";
import "../../styles/components/NotificationDropdown.scss";

const NotificationDropdown = () => {
  const {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    forceRefresh,
    lastUpdated, // Add this to track context updates
  } = useNotification();
  const { user: currentUser } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Log notifications changes for debugging
  useEffect(() => {
    console.log("🔄 NotificationDropdown: Notifications updated", {
      count: notifications.length,
      unreadCount,
      notifications: notifications.slice(0, 3), // Log first 3 for debugging
    });
  }, [notifications, unreadCount]);

  const handleToggleDropdown = () => {
    setIsOpen(!isOpen);
    // Force refresh when opening dropdown
    if (!isOpen && forceRefresh) {
      console.log("🔄 NotificationDropdown: Opening dropdown, forcing refresh");
      forceRefresh();
    }
  };

  const handleMarkAsRead = async (notificationId) => {
    console.log("📖 NotificationDropdown: Marking as read:", notificationId);
    await markAsRead(notificationId);
    // No need to force update - context will handle this naturally
  };

  const handleMarkAllAsRead = async () => {
    console.log("📖 NotificationDropdown: Marking all as read");
    await markAllAsRead();
    // No need to force update - context will handle this naturally
  };

  const handleDeleteNotification = async (notificationId) => {
    console.log(
      "🗑️ NotificationDropdown: Deleting notification:",
      notificationId
    );
    await deleteNotification(notificationId);
    // No need to force update - context will handle this naturally
  };

  const getNotificationAction = (notification) => {
    switch (notification.type || notification.Type) {
      case "Alert":
        return (
          <Link
            to="/member/blood-requests"
            className="notification-action"
            onClick={() => setIsOpen(false)}
          >
            Xem chi tiết
          </Link>
        );
      case "Reminder":
        return (
          <Link
            to="/member/activity-history"
            className="notification-action"
            onClick={() => setIsOpen(false)}
          >
            Xem chi tiết
          </Link>
        );
      case "Report":
        return (
          <Link
            to="/member/health-records"
            className="notification-action"
            onClick={() => setIsOpen(false)}
          >
            Xem báo cáo
          </Link>
        );
      // Legacy support
      case "urgent_request":
        return (
          <Link
            to="/member/blood-requests"
            className="notification-action"
            onClick={() => setIsOpen(false)}
          >
            Xem chi tiết
          </Link>
        );
      case "appointment_reminder":
      case "donation_reminder":
        return (
          <Link
            to="/member/activity-history"
            className="notification-action"
            onClick={() => setIsOpen(false)}
          >
            Xem chi tiết
          </Link>
        );
      default:
        return null;
    }
  };

  if (!currentUser) return null;

  return (
    <div className="notification-dropdown" ref={dropdownRef}>
      <button
        className="notification-trigger"
        onClick={handleToggleDropdown}
        aria-label="Thông báo"
      >
        <span className="notification-icon">🔔</span>
        {unreadCount > 0 && (
          <span className="notification-badge">
            {unreadCount > 99 ? "99+" : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="notification-dropdown-menu">
          <div className="notification-header">
            <h3>Thông báo</h3>
            {unreadCount > 0 && (
              <button
                className="mark-all-read-btn"
                onClick={handleMarkAllAsRead}
              >
                Đánh dấu tất cả đã đọc
              </button>
            )}
          </div>

          <div className="notification-list">
            {loading ? (
              <div className="notification-loading">
                <div className="loading-spinner"></div>
                <p>Đang tải thông báo...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="notification-empty">
                <span className="empty-icon">📭</span>
                <p>Không có thông báo nào</p>
              </div>
            ) : (
              notifications.slice(0, 10).map((notification) => (
                <div
                  key={notification.id}
                  className={`notification-item ${
                    !(notification.isRead || notification.IsRead)
                      ? "unread"
                      : ""
                  }`}
                >
                  <div className="notification-content">
                    <div className="notification-icon-wrapper">
                      <span
                        className="notification-type-icon"
                        style={{
                          color: NotificationService.getNotificationColor(
                            notification.type
                          ),
                        }}
                      >
                        {NotificationService.getNotificationIcon(
                          notification.type
                        )}
                      </span>
                      {!(notification.isRead || notification.IsRead) && (
                        <div className="unread-dot"></div>
                      )}
                    </div>

                    <div className="notification-body">
                      <div className="notification-title">
                        {notification.title}
                      </div>
                      <div className="notification-message">
                        {notification.message}
                      </div>
                      <div className="notification-time">
                        {NotificationService.formatNotificationTime(
                          notification.SentAt ||
                            notification.sentAt ||
                            notification.createdAt
                        )}
                      </div>

                      {getNotificationAction(notification)}
                    </div>

                    <div className="notification-actions">
                      {!(notification.isRead || notification.IsRead) && (
                        <button
                          className="mark-read-btn"
                          onClick={() => handleMarkAsRead(notification.id)}
                          title="Đánh dấu đã đọc"
                        >
                          ✓
                        </button>
                      )}
                      <button
                        className="delete-btn"
                        onClick={() =>
                          handleDeleteNotification(notification.id)
                        }
                        title="Xóa thông báo"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          <div className="notification-footer">
            <Link
              to="/member/notifications"
              className="view-all-link"
              onClick={() => setIsOpen(false)}
            >
              Xem tất cả thông báo
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
