import { useState, useEffect, useCallback } from "react";
import RemindService from "../services/remindService";
import { useAuth } from "../contexts/AuthContext";
import { useNotification } from "../contexts/NotificationContext";
import { toast } from "../utils/toastUtils";

/**
 * Custom hook for managing reminders
 */
const useRemindManagement = () => {
  const { user: currentUser } = useAuth();
  const { refreshNotifications } = useNotification();
  const [reminders, setReminders] = useState([]);
  const [loading, setLoading] = useState(false);

  /**
   * Load reminders for current user
   */
  const loadReminders = useCallback(
    async (userId = null) => {
      if (!userId && !currentUser?.id) return;

      setLoading(true);
      try {
        const targetUserId = userId || currentUser.id;
        const response = await RemindService.getUserReminders(targetUserId);

        // Handle response directly (consistent with other services)
        setReminders(Array.isArray(response) ? response : response?.data || []);
      } catch (error) {
        console.error("Error loading reminders:", error);

        // Handle 404 gracefully (no reminders found)
        if (error.response?.status === 404) {
          setReminders([]);
          console.log("No reminders found for user");
        } else {
          toast.error("Không thể tải danh sách nhắc nhở!");
          setReminders([]);
        }
      } finally {
        setLoading(false);
      }
    },
    [currentUser]
  );

  /**
   * Create a new reminder
   */
  const createReminder = async (reminderData) => {
    try {
      const response = await RemindService.createReminder(reminderData);

      toast.success("Đã tạo nhắc nhở thành công!");
      await loadReminders();

      // Refresh notifications to show new reminder
      await refreshNotifications();

      return response;
    } catch (error) {
      console.error("Error creating reminder:", error);
      toast.error("Không thể tạo nhắc nhở!");
      throw error;
    }
  };

  /**
   * Enable a reminder
   */
  const enableReminder = async (reminderId) => {
    try {
      const response = await RemindService.enableReminder(reminderId);

      toast.success("Đã bật nhắc nhở!");
      await loadReminders();

      // Refresh notifications to show updated reminder
      await refreshNotifications();

      return response;
    } catch (error) {
      console.error("Error enabling reminder:", error);
      toast.error("Không thể bật nhắc nhở!");
      throw error;
    }
  };

  /**
   * Disable a reminder
   */
  const disableReminder = async (reminderId) => {
    try {
      const response = await RemindService.disableReminder(reminderId);

      toast.success("Đã tắt nhắc nhở!");
      await loadReminders();
      return response;
    } catch (error) {
      console.error("Error disabling reminder:", error);
      toast.error("Không thể tắt nhắc nhở!");
      throw error;
    }
  };

  /**
   * Delete a reminder
   */
  const deleteReminder = async (reminderId) => {
    try {
      const response = await RemindService.deleteReminder(reminderId);

      toast.success("Đã xóa nhắc nhở!");
      await loadReminders();
      return response;
    } catch (error) {
      console.error("Error deleting reminder:", error);
      toast.error("Không thể xóa nhắc nhở!");
      throw error;
    }
  };

  /**
   * Create appointment reminder for a donation
   */
  const createAppointmentReminder = async (appointmentData) => {
    try {
      const response = await RemindService.createAppointmentReminder(
        appointmentData
      );

      toast.success(`Đã tạo nhắc nhở cho ${appointmentData.donorName}!`);
      await loadReminders();

      return response;
    } catch (error) {
      console.error("Error creating appointment reminder:", error);
      toast.error("Không thể tạo nhắc nhở lịch hẹn!");
      throw error;
    }
  };

  /**
   * Create manual reminder for a donation
   */
  const createManualReminder = async (donationData) => {
    try {
      const response = await RemindService.createManualReminder(donationData);

      toast.success(`Đã gửi nhắc nhở đến ${donationData.donorName}!`);
      await loadReminders();

      return response;
    } catch (error) {
      console.error("Error creating manual reminder:", error);
      toast.error("Không thể gửi nhắc nhở!");
      throw error;
    }
  };

  /**
   * Create donation eligibility reminder
   */
  const createDonationEligibilityReminder = async (userData) => {
    try {
      const response = await RemindService.createDonationEligibilityReminder(
        userData
      );

      if (response) {
        toast.success("Đã tạo nhắc nhở hiến máu!");
        await loadReminders();
        return response;
      } else {
        toast.info("Người dùng chưa đủ điều kiện nhận nhắc nhở hiến máu");
        return null;
      }
    } catch (error) {
      console.error("Error creating donation eligibility reminder:", error);
      toast.error("Không thể tạo nhắc nhở hiến máu!");
      throw error;
    }
  };

  /**
   * Get reminder statistics
   */
  const getReminderStats = useCallback(() => {
    const stats = {
      total: reminders.length,
      enabled: reminders.filter((r) => r.isEnabled).length,
      disabled: reminders.filter((r) => !r.isEnabled).length,
      byType: {},
    };

    // Count by type
    reminders.forEach((reminder) => {
      const type = reminder.type || "unknown";
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    });

    return stats;
  }, [reminders]);

  /**
   * Filter reminders by type
   */
  const filterRemindersByType = useCallback(
    (type) => {
      return reminders.filter((reminder) => reminder.type === type);
    },
    [reminders]
  );

  /**
   * Filter reminders by status
   */
  const filterRemindersByStatus = useCallback(
    (isEnabled) => {
      return reminders.filter((reminder) => reminder.isEnabled === isEnabled);
    },
    [reminders]
  );

  // Auto-load reminders when currentUser changes
  useEffect(() => {
    if (currentUser?.id) {
      loadReminders();
    }
  }, [currentUser, loadReminders]);

  return {
    // Data
    reminders,
    loading,
    currentUser,

    // Actions
    loadReminders,
    createReminder,
    enableReminder,
    disableReminder,
    deleteReminder,
    createAppointmentReminder,
    createManualReminder,
    createDonationEligibilityReminder,

    // Utilities
    getReminderStats,
    filterRemindersByType,
    filterRemindersByStatus,
  };
};

export default useRemindManagement;
