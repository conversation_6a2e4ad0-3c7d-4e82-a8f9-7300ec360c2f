import React from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Layout, Menu, Button, Avatar, Typography, Divider, Badge, Dropdown } from "antd";
import {
  HomeOutlined,
  CalendarOutlined,
  UserOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  EditOutlined,
  Bar<PERSON>hartOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ProfileOutlined,
  LockOutlined,
} from "@ant-design/icons";
import authService from "../../services/authService";
import { getUserName } from "../../utils/userUtils";
import { useBloodRequestCounts } from "../../hooks/useBloodRequestCounts";
import "../../styles/base/manager-design-system.scss";
import "../../styles/components/SidebarBadge.scss";
import logo from "../../assets/images/logo.jpg";

const { Sider } = Layout;
const { Text } = Typography;

const ManagerSidebar = ({ collapsed, onCollapse }) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Get blood request counts for sidebar badge
  const { total: bloodRequestCount, loading: countsLoading } =
    useBloodRequestCounts();

  const navItems = [
    {
      key: "/manager",
      label: "Dashboard",
      icon: <HomeOutlined />,
      path: "/manager",
      exact: true,
    },
    {
      key: "/manager/donation-schedule",
      label: "Lịch & Quy trình hiến máu",
      icon: <CalendarOutlined />,
      path: "/manager/donation-schedule",
    },
    {
      key: "/manager/eligible-donors",
      label: "Người hiến đủ điều kiện",
      icon: <UserOutlined />,
      path: "/manager/eligible-donors",
    },
    {
      key: "/manager/blood-requests",
      label: "Quản lý yêu cầu máu",
      icon: <FileTextOutlined />,
      path: "/manager/blood-requests",
      badge: bloodRequestCount > 0 ? bloodRequestCount : null,
    },
    {
      key: "/manager/blood-inventory",
      label: "Quản lý kho máu",
      icon: <DatabaseOutlined />,
      path: "/manager/blood-inventory",
    },
  ];

  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate("/");
    } catch (error) {
      console.error("Logout error:", error);
      navigate("/");
    }
  };

  // Dropdown menu items for user avatar
  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: 'Hồ sơ cá nhân',
      onClick: () => navigate('/manager/profile'),
    },
    {
      key: 'change-password',
      icon: <LockOutlined />,
      label: 'Đặt lại mật khẩu',
      onClick: () => navigate('/manager/change-password'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: handleLogout,
    },
  ];

  const getSelectedKey = () => {
    const currentPath = location.pathname;
    const exactMatch = navItems.find(
      (item) => item.exact && item.path === currentPath
    );
    if (exactMatch) return [exactMatch.key];

    const pathMatch = navItems.find(
      (item) => !item.exact && currentPath.startsWith(item.path)
    );
    return pathMatch ? [pathMatch.key] : [];
  };

  const menuItems = navItems.map((item) => ({
    key: item.key,
    icon: item.icon,
    label: (
      <Link to={item.path} style={{ color: "inherit", textDecoration: "none" }}>
        <div className="sidebar-menu-item">
          <span className="menu-label">{item.label}</span>
          {item.badge && (
            <Badge
              count={item.badge}
              size="small"
              className="sidebar-badge manager-badge menu-badge"
            />
          )}
        </div>
      </Link>
    ),
  }));

  const managerName = getUserName();

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={onCollapse}
      trigger={null}
      width={280}
      collapsedWidth={80}
      style={{
        position: "fixed",
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 1000,
        background: "#20374E",
      }}
      className="manager-sidebar"
    >
      <div
        style={{ height: "100vh", display: "flex", flexDirection: "column" }}
      >
        {/* Header */}
        <div
          style={{
            padding: collapsed ? "16px 8px" : "24px 16px",
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            textAlign: collapsed ? "center" : "left",
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => onCollapse(!collapsed)}
            style={{
              fontSize: "16px",
              width: collapsed ? 40 : 64,
              height: 40,
              color: "white",
              marginBottom: collapsed ? 0 : 16,
            }}
          />

          {collapsed && (
            <div style={{ textAlign: "center", marginBottom: "16px" }}>
              <img src={logo} alt="Ánh Dương Logo" style={{ height: '32px', width: 'auto' }} />
            </div>
          )}

          {!collapsed && (
            <>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  color: "white",
                  fontSize: "20px",
                  fontWeight: "bold",
                  fontFamily: "Inter, sans-serif",
                  marginBottom: "4px",
                }}
              >
                <img src={logo} alt="Ánh Dương Logo" style={{ height: '32px', width: 'auto' }} />
                <span>Ánh Dương</span>
              </div>
              <Text
                style={{
                  color: "rgba(255, 255, 255, 0.7)",
                  fontSize: "14px",
                  fontFamily: "Inter, sans-serif",
                }}
              >
                Hệ thống quản lý
              </Text>
            </>
          )}
        </div>

        {/* Navigation Menu */}
        <div style={{ flex: 1, overflow: "auto" }}>
          <Menu
            mode="inline"
            selectedKeys={getSelectedKey()}
            items={menuItems}
            style={{
              background: "transparent",
              border: "none",
              color: "white",
            }}
            theme="dark"
          />
        </div>

        {/* Footer */}
        <div
          style={{
            padding: collapsed ? "16px 8px" : "16px",
            borderTop: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          {!collapsed && (
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="topRight"
              trigger={['click']}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "12px",
                  color: "white",
                  cursor: "pointer",
                  padding: "8px",
                  borderRadius: "6px",
                  transition: "background-color 0.3s",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
              >
                <Avatar
                  size={32}
                  style={{ backgroundColor: "#D93E4C", marginRight: "8px" }}
                  icon={<UserOutlined />}
                />
                <Text
                  style={{
                    color: "white",
                    fontSize: "14px",
                    fontFamily: "Inter, sans-serif",
                  }}
                >
                  {managerName}
                </Text>
              </div>
            </Dropdown>
          )}

          {collapsed && (
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="topRight"
              trigger={['click']}
            >
              <Avatar
                size={32}
                style={{
                  backgroundColor: "#D93E4C",
                  cursor: "pointer",
                  margin: "0 auto",
                  display: "block"
                }}
                icon={<UserOutlined />}
              />
            </Dropdown>
          )}
        </div>
      </div>
    </Sider>
  );
};

export default ManagerSidebar;
