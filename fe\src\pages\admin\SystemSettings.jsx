import React, { useState, useEffect } from "react";
import {
  Card,
  Tabs,
  Form,
  Input,
  Switch,
  Button,
  Select,
  InputNumber,
  Space,
  Divider,
  Typography,
  Row,
  Col,
  Alert,
  Spin,
  Tag,
} from "antd";
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  SecurityScanOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import AdminLayout from "../../components/admin/AdminLayout";
import AdminPageHeader from "../../components/admin/AdminPageHeader";
import config from "../../config/environment";
import { apiClient } from "../../services/axiosInstance";
import userInfoService from "../../services/userInfoService";
import securityService from "../../services/securityService";
import { toast } from "../../utils/toastUtils";

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

const SystemSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [systemStats, setSystemStats] = useState({});
  const [blockedUsers, setBlockedUsers] = useState([]);

  // Load settings from SecurityService
  const [settings, setSettings] = useState({
    general: {
      hospitalName: "Bệnh viện Ánh Dương",
      systemName: "Hệ thống Hiến máu Ánh Dương",
      contactEmail: "<EMAIL>",
      contactPhone: "028-1234-5678",
      address: "CMT8 Street, District 3, Ho Chi Minh City",
      environment: config.app.environment,
    },
    security: securityService.getSettings(),
  });

  // Load system statistics and blocked users
  const loadSystemStats = async () => {
    try {
      const users = await userInfoService.getAllUsers();
      setSystemStats({
        totalUsers: users.length,
        activeUsers: users.filter((u) => u.status === 1).length,
        totalDoctors: users.filter((u) => u.roleID === 2).length,
        totalManagers: users.filter((u) => u.roleID === 3).length,
        totalAdmins: users.filter((u) => u.roleID === 4).length,
      });

      // Load blocked users
      const blocked = securityService.getBlockedUsers();
      setBlockedUsers(blocked);
    } catch (error) {
      console.error("Error loading system stats:", error);
    }
  };

  useEffect(() => {
    loadSystemStats();
    // Initialize form with current settings
    form.setFieldsValue(settings);
  }, []);

  const handleSave = async (values) => {
    setSaving(true);
    try {
      // Update local settings state
      setSettings((prev) => ({
        ...prev,
        ...values,
      }));

      // Save security settings to SecurityService
      if (values.security) {
        const result = securityService.saveSettings(values.security);
        if (!result.success) {
          throw new Error(result.error);
        }
      }

      toast.success("Cài đặt đã được lưu thành công!");
      toast.info(
        "Cài đặt bảo mật đã được áp dụng và sẽ có hiệu lực ngay lập tức."
      );

      // Reload stats to refresh blocked users
      loadSystemStats();
    } catch (error) {
      toast.error("Có lỗi xảy ra khi lưu cài đặt!");
      console.error("Error saving settings:", error);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(settings);
    toast.info("Đã khôi phục cài đặt ban đầu");
  };

  // Handle unblocking a user
  const handleUnblockUser = (email) => {
    const result = securityService.unblockUser(email);
    if (result.success) {
      toast.success(`Đã mở khóa tài khoản: ${email}`);
      loadSystemStats(); // Refresh blocked users list
    } else {
      toast.error("Có lỗi khi mở khóa tài khoản");
    }
  };

  // Clear all login attempts
  const handleClearAllAttempts = () => {
    const result = securityService.clearAllAttempts();
    if (result.success) {
      toast.success(`${result.message}`);
      loadSystemStats(); // Refresh blocked users list
    } else {
      toast.error("Có lỗi khi xóa lịch sử đăng nhập");
    }
  };

  const renderGeneralSettings = () => (
    <Card title="Thông tin chung" icon={<GlobalOutlined />}>
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Form.Item
            label="Tên bệnh viện"
            name={["general", "hospitalName"]}
            rules={[{ required: true, message: "Vui lòng nhập tên bệnh viện" }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            label="Tên hệ thống"
            name={["general", "systemName"]}
            rules={[{ required: true, message: "Vui lòng nhập tên hệ thống" }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            label="Email liên hệ"
            name={["general", "contactEmail"]}
            rules={[
              { required: true, message: "Vui lòng nhập email" },
              { type: "email", message: "Email không hợp lệ" },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            label="Số điện thoại"
            name={["general", "contactPhone"]}
            rules={[{ required: true, message: "Vui lòng nhập số điện thoại" }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col xs={24}>
          <Form.Item
            label="Địa chỉ"
            name={["general", "address"]}
            rules={[{ required: true, message: "Vui lòng nhập địa chỉ" }]}
          >
            <TextArea rows={2} />
          </Form.Item>
        </Col>
        <Col xs={24}>
          <Form.Item label="Môi trường">
            <Tag
              color={
                config.app.environment === "production" ? "green" : "orange"
              }
            >
              {config.app.environment.toUpperCase()}
            </Tag>
          </Form.Item>
        </Col>
      </Row>

      {/* System Statistics */}
      <Divider>Thống kê hệ thống</Divider>
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: "center" }}>
            <Text type="secondary">Tổng người dùng</Text>
            <div
              style={{ fontSize: "24px", fontWeight: "bold", color: "#1890ff" }}
            >
              {systemStats.totalUsers || 0}
            </div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: "center" }}>
            <Text type="secondary">Đang hoạt động</Text>
            <div
              style={{ fontSize: "24px", fontWeight: "bold", color: "#52c41a" }}
            >
              {systemStats.activeUsers || 0}
            </div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: "center" }}>
            <Text type="secondary">Bác sĩ</Text>
            <div
              style={{ fontSize: "24px", fontWeight: "bold", color: "#722ed1" }}
            >
              {systemStats.totalDoctors || 0}
            </div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: "center" }}>
            <Text type="secondary">Quản trị viên</Text>
            <div
              style={{ fontSize: "24px", fontWeight: "bold", color: "#fa8c16" }}
            >
              {systemStats.totalAdmins || 0}
            </div>
          </Card>
        </Col>
      </Row>
    </Card>
  );

  const renderSecuritySettings = () => (
    <Card title="Cài đặt bảo mật" icon={<SecurityScanOutlined />}>
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Form.Item
            label="Thời gian hết phiên (phút)"
            name={["security", "sessionTimeout"]}
            rules={[{ required: true, message: "Vui lòng nhập thời gian" }]}
          >
            <InputNumber min={5} max={120} style={{ width: "100%" }} />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            label="Độ dài mật khẩu tối thiểu"
            name={["security", "passwordMinLength"]}
            rules={[{ required: true, message: "Vui lòng nhập độ dài" }]}
          >
            <InputNumber min={6} max={20} style={{ width: "100%" }} />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            label="Số lần đăng nhập sai tối đa"
            name={["security", "maxLoginAttempts"]}
            rules={[{ required: true, message: "Vui lòng nhập số lần" }]}
          >
            <InputNumber min={3} max={10} style={{ width: "100%" }} />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            label="Yêu cầu mật khẩu mạnh"
            name={["security", "requireStrongPassword"]}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
      </Row>

      {/* Password Requirements Info */}
      <Divider>Yêu cầu mật khẩu hiện tại</Divider>
      <Alert
        message="Quy tắc xác thực mật khẩu"
        description={
          <div>
            <Text strong>Mật khẩu phải có tối thiểu 6 ký tự và bao gồm:</Text>
            <ul style={{ marginTop: 8, marginBottom: 0 }}>
              <li>Ít nhất 1 chữ cái viết hoa (A-Z)</li>
              <li>Ít nhất 1 chữ cái viết thường (a-z)</li>
              <li>Ít nhất 1 chữ số (0-9)</li>
              <li>Ít nhất 1 ký tự đặc biệt (!@#$%^&*)</li>
            </ul>
            <Text
              type="secondary"
              style={{ fontSize: "12px", marginTop: 8, display: "block" }}
            >
              ✅ Quy tắc này đã được áp dụng trong form đăng ký và đổi mật khẩu
            </Text>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* Login Attempts Info */}
      <Alert
        message="Giới hạn đăng nhập"
        description={
          <div>
            <Text>
              Hiện tại: <Text strong>Frontend validation only</Text>
            </Text>
            <ul style={{ marginTop: 8, marginBottom: 0 }}>
              <li>Kiểm tra email và mật khẩu format trước khi gửi request</li>
              <li>Hiển thị thông báo lỗi khi đăng nhập thất bại</li>
              <li>
                ⚠️ Chưa có logic khóa tài khoản sau{" "}
                {settings.security.maxLoginAttempts} lần thất bại
              </li>
            </ul>
            <Text
              type="secondary"
              style={{ fontSize: "12px", marginTop: 8, display: "block" }}
            >
              💡 Để triển khai đầy đủ, cần backend API hỗ trợ tracking login
              attempts
            </Text>
          </div>
        }
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* Blocked Users Management */}
      <Divider>Quản lý tài khoản bị khóa</Divider>
      {blockedUsers.length > 0 ? (
        <div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 16,
            }}
          >
            <Text strong>
              Danh sách tài khoản bị khóa ({blockedUsers.length})
            </Text>
            <Button
              type="primary"
              danger
              size="small"
              onClick={handleClearAllAttempts}
            >
              Xóa tất cả
            </Button>
          </div>
          <Table
            dataSource={blockedUsers}
            columns={[
              {
                title: "Email",
                dataIndex: "email",
                key: "email",
              },
              {
                title: "Số lần thử",
                dataIndex: "attempts",
                key: "attempts",
                width: 120,
              },
              {
                title: "Thời gian khóa",
                dataIndex: "blockedAt",
                key: "blockedAt",
                width: 150,
              },
              {
                title: "Còn lại (phút)",
                dataIndex: "remainingTime",
                key: "remainingTime",
                width: 120,
                render: (time) => <Text type="danger">{time}</Text>,
              },
              {
                title: "Thao tác",
                key: "action",
                width: 100,
                render: (_, record) => (
                  <Button
                    type="link"
                    size="small"
                    onClick={() => handleUnblockUser(record.email)}
                  >
                    Mở khóa
                  </Button>
                ),
              },
            ]}
            pagination={false}
            size="small"
            rowKey="email"
          />
        </div>
      ) : (
        <Alert
          message="Không có tài khoản nào bị khóa"
          description="Tất cả người dùng hiện tại đều có thể đăng nhập bình thường."
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Alert
        message="Lưu ý bảo mật"
        description="Các cài đặt bảo mật sẽ áp dụng cho tất cả người dùng trong hệ thống. Hãy cân nhắc kỹ trước khi thay đổi."
        type="warning"
        showIcon
      />
    </Card>
  );

  // Removed renderApiSettings function

  // Removed renderFeatureSettings function

  return (
    <AdminLayout>
      <AdminPageHeader
        title="Cài đặt hệ thống"
        icon={<SettingOutlined />}
        subtitle="Quản lý cấu hình và thiết lập hệ thống"
      />

      <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={settings}
          onFinish={handleSave}
        >
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            tabBarExtraContent={
              <Space>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  Khôi phục
                </Button>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  htmlType="submit"
                  loading={saving}
                >
                  Lưu cài đặt
                </Button>
              </Space>
            }
          >
            <TabPane
              tab={
                <span>
                  <GlobalOutlined />
                  Chung
                </span>
              }
              key="general"
            >
              {renderGeneralSettings()}
            </TabPane>

            <TabPane
              tab={
                <span>
                  <SecurityScanOutlined />
                  Bảo mật
                </span>
              }
              key="security"
            >
              {renderSecuritySettings()}
            </TabPane>
          </Tabs>
        </Form>
      </Card>
    </AdminLayout>
  );
};

export default SystemSettings;
