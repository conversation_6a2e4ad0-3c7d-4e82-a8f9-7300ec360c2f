// NOTE: Most data has been replaced by real APIs. This file is kept for:
// - Constants and enums still in use (BLOOD_GROUPS, RH_TYPES, REQUEST_STATUS, etc.)
// - Backward compatibility and fallback data
// - Files still using mock data: EmergencyRequestsManagement.jsx
// - Files using only constants: DonationProcessManagement.jsx, NotificationsManagement.jsx (use API + constants)

// User roles matching database Roles table
export const ROLES = {
  GUEST: "Guest",
  MEMBER: "1",
  STAFF_DOCTOR: "2",
  STAFF_BLOOD_MANAGER: "3",
  ADMIN: "4",
};

// User status matching database Users table
export const USER_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  BANNED: 2,
};

// Blood groups
export const BLOOD_GROUPS = {
  A: "A",
  B: "B",
  AB: "AB",
  O: "O",
};

// Rh types
export const RH_TYPES = {
  POSITIVE: "Rh+",
  NEGATIVE: "Rh-",
};

// Blood request status matching API specification
export const REQUEST_STATUS = {
  PENDING: 0, // Đang chờ xử lý
  ACCEPTED: 1, // Chấp nhận
  COMPLETED: 2, // Hoàn thành
  REJECTED: 3, // Từ chối
};

// Blood request urgency levels
export const URGENCY_LEVELS = {
  NORMAL: 0,
  URGENT: 1,
  CRITICAL: 2,
};

// Doctor types for different departments
export const DOCTOR_TYPES = {
  BLOOD_DEPARTMENT: "blood_department",
  OTHER_DEPARTMENT: "other_department",
};

// Department IDs mapping
export const DEPARTMENT_IDS = {
  "Khoa Huyết học": 1,
  "Khoa Tim mạch": 2,
  "Khoa Nhi": 3,
  "Khoa Cấp Cứu": 4,
  "Khoa Giải phẫu": 5,
  "Khoa Ngoại": 6,
};

export const mockUsers = [];

export const mockDonationHistory = [];
// Helper functions for authentication and user management
export const authenticateUser = (email, password) => {
  return mockUsers.find(
    (user) => user.email === email && user.password === password
  );
};

export const getUserById = (id) => {
  return mockUsers.find((user) => user.userID === id || user.id === id);
};

export const getDonationHistoryByDonor = (userID) => {
  return mockDonationHistory.filter((donation) => donation.userID === userID);
};

// Hospital information
export const HOSPITAL_INFO = {
  name: "Bệnh viện Đa khoa Ánh Dương",
  address: "123 Đường ABC, Quận 1, TP.HCM",
  phone: "028-1234-5678",
  email: "info@anhdương.com",
  website: "https://anhduong.com",
  latitude: 10.7751237,
  longitude: 106.6862143,
};

// Mock hospital info matching database HospitalInfo table
export const mockHospitalInfo = {
  id: 1,
  name: "Bệnh viện Đa khoa Ánh Dương",
  address: "Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam",
  phone: "(028) 3957 1343",
  email: "<EMAIL>",
  workingHours: "Thứ 2 - Chủ nhật: 07:00 - 17:00",
  mapImageUrl:
    "https://maps.googleapis.com/maps/api/staticmap?center=10.7751237,106.6862143&zoom=15&size=600x400&key=YOUR_API_KEY",
  latitude: 10.7751237,
  longitude: 106.6862143,
};
