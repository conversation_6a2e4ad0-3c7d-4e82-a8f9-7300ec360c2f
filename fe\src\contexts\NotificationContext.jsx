import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useAuth } from "./AuthContext";
import NotificationService from "../services/notificationService";
import { toast } from "../utils/toastUtils";

const NotificationContext = createContext();

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(Date.now()); // Add timestamp for re-render trigger

  // Load notifications when user changes
  const loadNotifications = useCallback(
    async (forceRefresh = false) => {
      if (!isAuthenticated || !user?.id) {
        setNotifications([]);
        setUnreadCount(0);
        return;
      }

      try {
        setLoading(true);

        const response = await NotificationService.getNotifications(user.id);

        if (response && Array.isArray(response)) {
          const filteredNotifications =
            NotificationService.filterActiveNotifications(response);

          // Use immutable update pattern
          setNotifications(filteredNotifications);

          // Calculate unread count
          const unread = filteredNotifications.filter(
            (n) => !n.isRead && !n.IsRead
          ).length;
          setUnreadCount(unread);
          setLastUpdated(Date.now()); // Trigger component updates
        } else {
          setNotifications([]);
          setUnreadCount(0);
        }
      } catch (error) {
        setNotifications([]);
        setUnreadCount(0);
      } finally {
        setLoading(false);
      }
    },
    [isAuthenticated, user?.id]
  );

  // Load notifications on mount and when user changes
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // Mark single notification as read
  const markAsRead = async (notificationId) => {
    try {
      const result = await NotificationService.markAsRead(notificationId);

      if (result.success || result) {
        // Update local state immediately with proper immutability
        setNotifications((prev) =>
          prev.map((notification) => {
            const isTarget =
              notification.id === notificationId ||
              notification.NotificationID === notificationId ||
              notification.notificationId === notificationId ||
              String(notification.id) === String(notificationId) ||
              String(notification.NotificationID) === String(notificationId);

            if (isTarget) {
              return {
                ...notification,
                isRead: true,
                IsRead: true,
                read: true, // Add all possible read flags
              };
            }
            return notification;
          })
        );

        // Update unread count
        setUnreadCount((prev) => {
          const newCount = Math.max(0, prev - 1);
          return newCount;
        });
        toast.success("Đã đánh dấu thông báo đã đọc");
        setLastUpdated(Date.now()); // Trigger component updates

        return true;
      } else {
        toast.error("Không thể đánh dấu thông báo đã đọc");
        return false;
      }
    } catch (error) {
      toast.error("Không thể đánh dấu thông báo đã đọc");
      return false;
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user?.id) return false;

    try {
      const result = await NotificationService.markAllAsRead(user.id);

      if (result.success || result) {
        // Update all notifications to read status immediately
        setNotifications((prev) =>
          prev.map((notification) => ({
            ...notification,
            isRead: true,
            IsRead: true,
          }))
        );

        // Reset unread count to 0
        setUnreadCount(0);

        toast.success("Đã đánh dấu tất cả thông báo đã đọc");
        setLastUpdated(Date.now()); // Trigger component updates
        return true;
      } else {
        toast.error("Không thể đánh dấu tất cả thông báo đã đọc");
        return false;
      }
    } catch (error) {
      toast.error("Không thể đánh dấu tất cả thông báo đã đọc");
      return false;
    }
  };

  // Delete single notification
  const deleteNotification = async (notificationId) => {
    try {
      const result = await NotificationService.deleteNotification(
        notificationId
      );

      if (result.success || result) {
        // Find and remove from local state immediately with better ID matching
        const deletedNotification = notifications.find(
          (n) =>
            n.id === notificationId ||
            n.NotificationID === notificationId ||
            n.notificationId === notificationId ||
            String(n.id) === String(notificationId) ||
            String(n.NotificationID) === String(notificationId)
        );



        setNotifications((prev) =>
          prev.filter(
            (n) =>
              n.id !== notificationId &&
              n.NotificationID !== notificationId &&
              n.notificationId !== notificationId &&
              String(n.id) !== String(notificationId) &&
              String(n.NotificationID) !== String(notificationId)
          )
        );

        // Update unread count if deleted notification was unread
        if (
          deletedNotification &&
          !deletedNotification.isRead &&
          !deletedNotification.IsRead
        ) {
          setUnreadCount((prev) => {
            const newCount = Math.max(0, prev - 1);
            return newCount;
          });
        }
        toast.success("🗑️ Đã xóa thông báo");
        setLastUpdated(Date.now()); // Trigger component updates

        return true;
      } else {
        toast.error("Không thể xóa thông báo");
        return false;
      }
    } catch (error) {
      toast.error("Không thể xóa thông báo");
      return false;
    }
  };

  // Force refresh notifications from API
  const forceRefresh = useCallback(async () => {
    await loadNotifications(true);
  }, [loadNotifications]);

  // Add new notification (for real-time updates)
  const addNotification = useCallback((newNotification) => {
    setNotifications((prev) => [newNotification, ...prev]);

    // Update unread count if new notification is unread
    if (!newNotification.isRead && !newNotification.IsRead) {
      setUnreadCount((prev) => prev + 1);
    }

    setLastUpdated(Date.now()); // Trigger component updates
  }, []);

  // Get notifications by type
  const getNotificationsByType = useCallback(
    (type) => {
      return notifications.filter((n) => n.type === type);
    },
    [notifications]
  );

  // Get unread notifications
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter((n) => !n.isRead && !n.IsRead);
  }, [notifications]);

  // Clear all notifications (for logout)
  const clearNotifications = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
    setLastUpdated(Date.now());
  }, []);

  const value = {
    // State
    notifications,
    unreadCount,
    loading,
    lastUpdated, // Include for components that need to track updates

    // Actions
    loadNotifications,
    forceRefresh, // Add this for manual refresh
    markAsRead,
    markAllAsRead,
    deleteNotification,
    addNotification,
    clearNotifications,

    // Getters
    getNotificationsByType,
    getUnreadNotifications,

    // Computed values
    hasUnread: unreadCount > 0,
    totalCount: notifications.length,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
